<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            display: none;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .loading {
            text-align: center;
            color: #007bff;
        }
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图像质量AI分析应用 - 功能测试</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 点击或拖拽图片到这里上传</p>
            <p style="color: #666; font-size: 14px;">支持 JPG, PNG, WebP 格式</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div id="imagePreview"></div>
        
        <div style="text-align: center;">
            <button class="btn" id="analyzeBtn" disabled>🔍 AI质量分析</button>
            <button class="btn" id="upscaleBtn" disabled>🚀 图像超分 (2x)</button>
        </div>
        
        <div id="progress" class="progress" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let selectedFile = null;
        let imageBase64 = null;

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const imagePreview = document.getElementById('imagePreview');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const upscaleBtn = document.getElementById('upscaleBtn');
        const result = document.getElementById('result');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');

        // 上传区域点击事件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', handleFileSelect);

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showResult('❌ 请选择图片文件', 'error');
                return;
            }

            selectedFile = file;
            
            // 显示图片预览
            const reader = new FileReader();
            reader.onload = (e) => {
                imageBase64 = e.target.result;
                imagePreview.innerHTML = `
                    <img src="${imageBase64}" class="image-preview" alt="预览图片">
                    <p>文件名: ${file.name}</p>
                    <p>大小: ${(file.size / 1024).toFixed(2)} KB</p>
                `;
                
                // 启用按钮
                analyzeBtn.disabled = false;
                upscaleBtn.disabled = false;
                
                showResult('✅ 图片上传成功，可以开始分析或超分处理', 'success');
            };
            reader.readAsDataURL(file);
        }

        // AI分析按钮事件
        analyzeBtn.addEventListener('click', async () => {
            if (!imageBase64) {
                showResult('❌ 请先上传图片', 'error');
                return;
            }

            showProgress('🔍 正在进行AI质量分析...');
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageBase64: imageBase64
                    })
                });

                const data = await response.json();
                hideProgress();

                if (response.ok && data.success) {
                    showResult(`
                        <h3>🎯 AI质量分析结果</h3>
                        <p><strong>质量评分:</strong> ${data.score}/10</p>
                        <p><strong>图片格式:</strong> ${data.analysis.format.toUpperCase()}</p>
                        <p><strong>文件大小:</strong> ${data.analysis.quality_factors.file_size}</p>
                        <p><strong>分辨率等级:</strong> ${data.analysis.quality_factors.resolution}</p>
                        <p><strong>分析时间:</strong> ${data.processing_time_ms}ms</p>
                        <p style="color: #666; font-size: 14px;">${data.message}</p>
                    `, 'success');
                } else {
                    showResult(`❌ 分析失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                hideProgress();
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        });

        // 超分按钮事件
        upscaleBtn.addEventListener('click', async () => {
            if (!imageBase64) {
                showResult('❌ 请先上传图片', 'error');
                return;
            }

            showProgress('🚀 正在进行图像超分处理...');
            
            try {
                const response = await fetch('/api/upscale', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageBase64: imageBase64,
                        scale: 2,
                        face_enhance: true,
                        model: 'real-esrgan'
                    })
                });

                const data = await response.json();
                hideProgress();

                if (response.ok && data.success) {
                    showResult(`
                        <h3>🎉 图像超分处理完成</h3>
                        <p><strong>处理模型:</strong> ${data.model}</p>
                        <p><strong>缩放倍数:</strong> ${data.scale}x</p>
                        <p><strong>面部增强:</strong> ${data.face_enhance ? '已启用' : '未启用'}</p>
                        <p><strong>处理时间:</strong> ${data.processing_time_ms}ms</p>
                        <div style="margin-top: 15px;">
                            <a href="${data.upscaled_image}" target="_blank" class="btn">📥 下载超分图片</a>
                        </div>
                        <div style="margin-top: 15px;">
                            <img src="${data.upscaled_image}" class="image-preview" alt="超分处理后的图片">
                        </div>
                        <p style="color: #666; font-size: 14px;">${data.message}</p>
                    `, 'success');
                } else {
                    showResult(`❌ 超分处理失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                hideProgress();
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        });

        function showProgress(message) {
            progress.style.display = 'block';
            progressBar.style.width = '100%';
            showResult(`<div class="loading">${message}</div>`, '');
        }

        function hideProgress() {
            progress.style.display = 'none';
            progressBar.style.width = '0%';
        }

        function showResult(message, type) {
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        // 页面加载完成后检查API服务器状态
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                if (response.ok) {
                    showResult('✅ API服务器连接正常，可以开始使用', 'success');
                } else {
                    showResult('⚠️ API服务器连接异常', 'error');
                }
            } catch (error) {
                showResult('❌ 无法连接到API服务器，请确保本地服务器已启动', 'error');
            }
        });
    </script>
</body>
</html>