{"name": "image-quality-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^4.30.7", "@stripe/stripe-js": "^3.3.0", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^5.1.0", "formidable": "^3.5.1", "micro": "^10.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.7.1", "replicate": "^0.25.2", "stripe": "^14.21.0"}, "devDependencies": {"@types/formidable": "^3.4.5", "@types/micro": "^7.3.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vercel/node": "^3.0.21", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0"}}