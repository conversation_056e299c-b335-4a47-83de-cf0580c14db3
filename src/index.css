:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* 自定义样式 */
.upload-area {
  border: 2px dashed #646cff;
  border-radius: 8px;
  padding: 2rem;
  margin: 1rem 0;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #535bf2;
}

.upload-area.dragover {
  border-color: #535bf2;
  background-color: rgba(83, 91, 242, 0.1);
}

.analyze-btn {
  background-color: #646cff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin: 1rem;
  transition: background-color 0.3s;
}

.analyze-btn:hover:not(:disabled) {
  background-color: #535bf2;
}

.analyze-btn:disabled {
  background-color: #888;
  cursor: not-allowed;
}

.score-display {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.image-preview {
  max-width: 300px;
  max-height: 300px;
  margin: 1rem auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.auth-section {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #333;
  border-radius: 8px;
}

.premium-banner {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-weight: bold;
}

/* 超分功能样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.upscale-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.upscale-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.upscale-controls label {
  font-size: 0.9rem;
  color: #ccc;
}

.upscale-controls select {
  background-color: #333;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.9rem;
}

.upscale-btn {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.upscale-btn:hover:not(:disabled) {
  background-color: #059669;
}

.upscale-btn:disabled {
  background-color: #888;
  cursor: not-allowed;
}

.upscale-result {
  margin: 2rem 0;
  padding: 1.5rem;
  border: 1px solid #333;
  border-radius: 8px;
  background-color: rgba(16, 185, 129, 0.1);
}

.upscale-result h3 {
  margin-top: 0;
  color: #10b981;
}

.image-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.comparison-item {
  text-align: center;
}

.comparison-item h4 {
  margin-bottom: 0.5rem;
  color: #ccc;
  font-size: 1rem;
}

.download-btn {
  display: inline-block;
  background-color: #646cff;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.download-btn:hover {
  background-color: #535bf2;
}

@media (max-width: 768px) {
  .image-comparison {
    grid-template-columns: 1fr;
  }

  .button-group {
    align-items: center;
  }

  .upscale-section {
    width: 100%;
  }
}